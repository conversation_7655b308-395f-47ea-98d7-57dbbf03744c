#!/bin/bash

# Claude Code 配置脚本
# 使用方法: source setup-claude-code.sh

echo "🔧 配置 Claude Code 连接到本地 Kiro API 服务器..."
echo ""

# 清除可能存在的旧环境变量
unset ANTHROPIC_AUTH_TOKEN
unset ANTHROPIC_BASE_URL

# 设置新的环境变量
export ANTHROPIC_API_KEY=123456
export ANTHROPIC_BASE_URL=http://localhost:3000

echo "✅ 环境变量已设置:"
echo "   ANTHROPIC_API_KEY=123456"
echo "   ANTHROPIC_BASE_URL=http://localhost:3000"
echo ""
echo "🚀 现在可以启动 Claude Code:"
echo "   claude"
echo ""
echo "💡 如果遇到连接问题，请尝试:"
echo "   1. 确保服务器正在运行 (http://localhost:3000/health)"
echo "   2. 尝试不同的环境变量组合:"
echo "      export ANTHROPIC_BASE_URL=http://localhost:3000/v1"
echo "   3. 检查 <PERSON> Code 版本是否支持自定义端点"
echo ""
