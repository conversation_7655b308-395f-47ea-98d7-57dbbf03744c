#!/bin/bash

# Kiro API 服务器启动脚本
# 使用方法: ./start-kiro-server.sh

echo "🚀 启动 Kiro API 服务器..."
echo "📍 项目目录: $(pwd)"
echo "🔑 使用 Kiro 认证文件: kiro-auth-token.json"
echo "🌐 服务器地址: http://localhost:3000"
echo "🔐 API Key: 123456"
echo ""

# 检查配置文件是否存在
if [ ! -f "config.json" ]; then
    echo "❌ 错误: config.json 文件不存在"
    exit 1
fi

# 检查 Kiro 认证文件是否存在
if [ ! -f "kiro-auth-token.json" ]; then
    echo "❌ 错误: kiro-auth-token.json 文件不存在"
    exit 1
fi

# 检查 Node.js 依赖是否已安装
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖包..."
    npm install
fi

echo "✅ 启动服务器..."
echo "💡 提示: 在另一个终端中运行以下命令来配置 Claude Code:"
echo "   export ANTHROPIC_API_KEY=123456"
echo "   export ANTHROPIC_BASE_URL=http://localhost:3000"
echo "   claude"
echo ""
echo "🛑 按 Ctrl+C 停止服务器"
echo "----------------------------------------"

# 启动服务器
node src/api-server.js
