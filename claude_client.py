#!/usr/bin/env python3
"""
简单的 Claude API 客户端
支持连接到本地 Kiro API 服务器
"""

import requests
import json
import sys

# 配置
API_KEY = "123456"
BASE_URL = "http://localhost:3000"
MODEL = "claude-sonnet-4-20250514"

def chat_with_claude(message, stream=False):
    """与 Claude 进行对话"""
    url = f"{BASE_URL}/v1/messages"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {API_KEY}"
    }
    
    data = {
        "model": MODEL,
        "max_tokens": 2000,
        "messages": [
            {"role": "user", "content": message}
        ]
    }
    
    if stream:
        data["stream"] = True
        
    try:
        if stream:
            # 流式响应
            response = requests.post(url, headers=headers, json=data, stream=True)
            response.raise_for_status()
            
            print("Claude: ", end="", flush=True)
            for line in response.iter_lines():
                if line:
                    line_str = line.decode('utf-8')
                    if line_str.startswith('data: '):
                        data_str = line_str[6:]  # 移除 'data: ' 前缀
                        if data_str.strip() == '[DONE]':
                            break
                        try:
                            chunk_data = json.loads(data_str)
                            if chunk_data.get('type') == 'content_block_delta':
                                delta = chunk_data.get('delta', {})
                                if delta.get('type') == 'text_delta':
                                    print(delta.get('text', ''), end='', flush=True)
                        except json.JSONDecodeError:
                            continue
            print()  # 换行
        else:
            # 非流式响应
            response = requests.post(url, headers=headers, json=data)
            response.raise_for_status()
            
            result = response.json()
            content = result.get('content', [])
            if content and len(content) > 0:
                text_content = content[0].get('text', '')
                print(f"Claude: {text_content}")
            else:
                print("Claude: (无响应内容)")
                
    except requests.exceptions.RequestException as e:
        print(f"❌ 连接错误: {e}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ JSON 解析错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False
        
    return True

def check_server_health():
    """检查服务器健康状态"""
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ 服务器状态: {health_data.get('status')}")
            print(f"📅 时间戳: {health_data.get('timestamp')}")
            print(f"🔧 提供商: {health_data.get('provider')}")
            return True
        else:
            print(f"❌ 服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        return False

def interactive_chat():
    """交互式聊天"""
    print("🤖 Claude 客户端 (连接到本地 Kiro API)")
    print("💡 输入 'quit' 或 'exit' 退出")
    print("💡 输入 'stream' 切换流式模式")
    print("💡 输入 'health' 检查服务器状态")
    print("-" * 50)
    
    # 检查服务器状态
    if not check_server_health():
        print("请确保服务器正在运行: node src/api-server.js")
        return
    
    stream_mode = False
    
    while True:
        try:
            user_input = input("\n你: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 再见！")
                break
            elif user_input.lower() == 'stream':
                stream_mode = not stream_mode
                print(f"🔄 流式模式: {'开启' if stream_mode else '关闭'}")
                continue
            elif user_input.lower() == 'health':
                check_server_health()
                continue
            elif not user_input:
                continue
                
            # 发送消息
            chat_with_claude(user_input, stream=stream_mode)
            
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except EOFError:
            print("\n👋 再见！")
            break

if __name__ == "__main__":
    if len(sys.argv) > 1:
        # 命令行模式
        message = " ".join(sys.argv[1:])
        chat_with_claude(message)
    else:
        # 交互式模式
        interactive_chat()
